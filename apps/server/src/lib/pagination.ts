/**
 * Generic Pagination System
 *
 * Based on the Pagination Utilities pattern with proper generics
 * Works with all Prisma models while maintaining our existing API design
 */

import type {
	CursorPaginationParams,
	CursorPaginationResponse,
	PaginationResponse,
	SimplePaginationParams,
	StructuredPaginationParams,
} from "@saas-template/schemas";
import {
	parseSortString,
	transformSimpleToPrismaHelpers,
	transformStructuredToPrismaHelpers,
} from "@saas-template/schemas";
import prisma from "../../prisma";
import type { Prisma, PrismaClient } from "../../prisma/generated/client";

// ===== GENERIC TYPES =====

// Extract model names from PrismaClient (same as Pagination Utilities)
type PrismaModels = keyof Omit<
	PrismaClient,
	"$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends"
>;

// Generic pagination parameters that work with any model
type GenericPaginationArgs<T extends PrismaModels> = {
	page?: number;
	limit?: number;
	where?: Prisma.Args<PrismaClient[T], "findMany">["where"];
	orderBy?: Prisma.Args<PrismaClient[T], "findMany">["orderBy"];
	include?: Prisma.Args<PrismaClient[T], "findMany">["include"];
	select?: Prisma.Args<PrismaClient[T], "findMany">["select"];
};

// Generic cursor pagination parameters
type GenericCursorArgs<T extends PrismaModels> = {
	cursor?: string;
	limit?: number;
	where?: Prisma.Args<PrismaClient[T], "findMany">["where"];
	orderBy?: Prisma.Args<PrismaClient[T], "findMany">["orderBy"];
	include?: Prisma.Args<PrismaClient[T], "findMany">["include"];
	select?: Prisma.Args<PrismaClient[T], "findMany">["select"];
};

// ===== CORE GENERIC FUNCTIONS =====

/**
 * Generic pagination function that works with any Prisma model
 * Returns our standard PaginationResponse format
 */
async function paginateModel<T extends PrismaModels>(
	modelName: T,
	args: GenericPaginationArgs<T>,
): Promise<
	PaginationResponse<Prisma.Result<PrismaClient[T], typeof args, "findMany">>
> {
	const { page = 1, limit = 10, where, orderBy, include, select } = args;

	const model = prisma[modelName];

	// Use Promise.all for better performance
	const [data, total] = await Promise.all([
		(model as any).findMany({
			where,
			orderBy,
			include,
			select,
			skip: (page - 1) * limit,
			take: limit,
		}),
		(model as any).count({ where }),
	]);

	const totalPages = Math.ceil(total / limit);

	return {
		data,
		meta: {
			total,
			page,
			limit,
			totalPages,
			hasNext: page < totalPages,
			hasPrev: page > 1,
		},
	};
}

/**
 * Generic cursor pagination function
 * Returns our standard CursorPaginationResponse format
 */
async function paginateModelCursor<T extends PrismaModels>(
	modelName: T,
	args: GenericCursorArgs<T>,
): Promise<
	CursorPaginationResponse<
		Prisma.Result<PrismaClient[T], typeof args, "findMany">
	>
> {
	const { cursor, limit = 20, where, orderBy, include, select } = args;

	const model = prisma[modelName];

	const data = await (model as any).findMany({
		where,
		orderBy,
		include,
		select,
		cursor: cursor ? { id: cursor } : undefined,
		skip: cursor ? 1 : 0,
		take: limit + 1, // Take one extra to check if there's a next page
	});

	const hasNext = data.length > limit;
	const items = hasNext ? data.slice(0, -1) : data;
	const nextCursor = hasNext ? items[items.length - 1]?.id : undefined;

	return {
		data: items,
		meta: {
			limit,
			hasNext,
			hasPrev: !!cursor,
			nextCursor,
		},
	};
}

// ===== MODEL CONFIGURATION =====

// Search fields for each model
const MODEL_SEARCH_FIELDS = {
	user: ["name", "email"] as string[],
	organization: ["name", "description"] as string[],
	organizationMember: ["user.name", "user.email"] as string[],
	invitation: ["email"] as string[],
} as const;

// Helper function to safely get search fields
function getSearchFields(modelName: string): string[] {
	return (MODEL_SEARCH_FIELDS as Record<string, string[]>)[modelName] || [];
}

// Helper function to safely get model defaults
function getModelDefaults(modelName: string): any {
	return (MODEL_DEFAULTS as Record<string, any>)[modelName] || {};
}

// Default select/include configurations for performance
const MODEL_DEFAULTS = {
	user: {
		select: {
			id: true,
			name: true,
			email: true,
			emailVerified: true,
			image: true,
			createdAt: true,
			updatedAt: true,
		},
	},
	organization: {
		select: {
			id: true,
			name: true,
			slug: true,
			description: true,
			type: true,
			createdAt: true,
			updatedAt: true,
		},
	},
	organizationMember: {
		include: {
			user: {
				select: {
					id: true,
					name: true,
					email: true,
					image: true,
				},
			},
		},
	},
	invitation: {
		include: {
			organization: {
				select: {
					id: true,
					name: true,
				},
			},
			invitedBy: {
				select: {
					id: true,
					name: true,
				},
			},
		},
	},
} as const;

// ===== TRANSFORMATION FUNCTIONS =====

/**
 * Transform SimplePaginationParams to GenericPaginationArgs
 */
function transformSimpleToGeneric<T extends PrismaModels>(
	params: SimplePaginationParams,
	modelName: T,
	searchFields: string[] = getSearchFields(modelName as string),
): GenericPaginationArgs<T> {
	const prismaParams = transformSimpleToPrismaHelpers(params, searchFields);

	return {
		page: prismaParams.page,
		limit: prismaParams.limit,
		where: prismaParams.filters as any,
		orderBy: prismaParams.orderBy as any,
		...getModelDefaults(modelName as string),
	};
}

/**
 * Transform StructuredPaginationParams to GenericPaginationArgs
 */
function transformStructuredToGeneric<T extends PrismaModels>(
	params: StructuredPaginationParams,
	modelName: T,
	searchFields: string[] = getSearchFields(modelName as string),
): GenericPaginationArgs<T> {
	const prismaParams = transformStructuredToPrismaHelpers(params, searchFields);

	return {
		page: prismaParams.page,
		limit: prismaParams.limit,
		where: prismaParams.filters as any,
		orderBy: prismaParams.orderBy as any,
		include: params.include as any,
		select: params.select as any,
	};
}

/**
 * Transform CursorPaginationParams to GenericCursorArgs
 */
function transformCursorToGeneric<T extends PrismaModels>(
	params: CursorPaginationParams,
	modelName: T,
	searchFields: string[] = getSearchFields(modelName as string),
): GenericCursorArgs<T> {
	// Handle search
	const searchWhere = params.search
		? typeof params.search === "string"
			? {
					OR: searchFields.map((field) => ({
						[field]: {
							contains: params.search as string,
							mode: "insensitive",
						},
					})),
				}
			: {
					OR: (params.search.fields || searchFields).map((field) => ({
						[field]: {
							contains: (params.search as { term: string; fields?: string[]; mode?: string }).term,
							mode: (params.search as { term: string; fields?: string[]; mode?: string }).mode || "insensitive",
						},
					})),
				}
		: undefined;

	// Handle sort
	const orderBy = params.sort
		? typeof params.sort === "string"
			? (() => {
					const { field, order } = parseSortString(params.sort as string);
					return { [field]: order };
				})()
			: { [(params.sort as { field: string; order: string }).field]: (params.sort as { field: string; order: string }).order }
		: { createdAt: "desc" }; // Default sort for cursor pagination

	// Combine where conditions
	const where = searchWhere
		? params.filter
			? { AND: [searchWhere, params.filter] }
			: searchWhere
		: params.filter;

	return {
		cursor: params.cursor,
		limit: params.limit,
		where: where as any,
		orderBy: orderBy as any,
		include: params.include as any,
		select: params.select as any,
	};
}

// ===== API LAYER FUNCTIONS =====

/**
 * Simple pagination for users
 */
async function paginateUsers(params: SimplePaginationParams) {
	const args = transformSimpleToGeneric(params, "user");
	return paginateModel("user", args);
}

/**
 * Simple pagination for organizations
 */
async function paginateOrganizations(params: SimplePaginationParams) {
	const args = transformSimpleToGeneric(params, "organization");
	return paginateModel("organization", args);
}

/**
 * Simple pagination for organization members
 */
async function paginateOrganizationMembers(params: SimplePaginationParams) {
	const args = transformSimpleToGeneric(params, "organizationMember");
	return paginateModel("organizationMember", args);
}

/**
 * Simple pagination for invitations
 */
async function paginateInvitations(params: SimplePaginationParams) {
	const args = transformSimpleToGeneric(params, "invitation");
	return paginateModel("invitation", args);
}

/**
 * Structured pagination for users with advanced options
 */
async function paginateUsersStructured(params: StructuredPaginationParams) {
	const args = transformStructuredToGeneric(params, "user");
	return paginateModel("user", args);
}

/**
 * Structured pagination for organizations with advanced options
 */
async function paginateOrganizationsStructured(
	params: StructuredPaginationParams,
) {
	const args = transformStructuredToGeneric(params, "organization");
	return paginateModel("organization", args);
}

/**
 * Structured pagination for organization members with advanced options
 */
async function paginateOrganizationMembersStructured(
	params: StructuredPaginationParams,
) {
	const args = transformStructuredToGeneric(params, "organizationMember");
	return paginateModel("organizationMember", args);
}

/**
 * Cursor pagination for users (infinite scroll)
 */
async function paginateUsersCursor(params: CursorPaginationParams) {
	const args = transformCursorToGeneric(params, "user");
	return paginateModelCursor("user", args);
}

/**
 * Cursor pagination for organizations
 */
async function paginateOrganizationsCursor(params: CursorPaginationParams) {
	const args = transformCursorToGeneric(params, "organization");
	return paginateModelCursor("organization", args);
}

/**
 * Generic advanced pagination - works with any model
 */
async function paginateAdvanced<T extends PrismaModels>(
	modelName: T,
	params: {
		pagination:
			| { type: "page"; page?: number; limit?: number }
			| { type: "cursor"; cursor?: string; limit?: number };
		where?: Prisma.Args<PrismaClient[T], "findMany">["where"];
		orderBy?: Prisma.Args<PrismaClient[T], "findMany">["orderBy"];
		include?: Prisma.Args<PrismaClient[T], "findMany">["include"];
		select?: Prisma.Args<PrismaClient[T], "findMany">["select"];
	},
) {
	if (params.pagination.type === "cursor") {
		return paginateModelCursor(modelName, {
			cursor: params.pagination.cursor,
			limit: params.pagination.limit,
			where: params.where,
			orderBy: params.orderBy,
			include: params.include,
			select: params.select,
		});
	}
	return paginateModel(modelName, {
		page: params.pagination.page,
		limit: params.pagination.limit,
		where: params.where,
		orderBy: params.orderBy,
		include: params.include,
		select: params.select,
	});
}

// ===== MAIN PAGINATION API =====

/**
 * Main pagination API with discoverable structure
 *
 * Simple Layer: paginate.users({ page: 1, limit: 10, search: "john", sort: "name:asc" })
 * Structured Layer: paginate.structured.users({ ... })
 * Cursor Layer: paginate.cursor.users({ ... })
 * Advanced Layer: paginate.advanced("user", { ... })
 */
export const paginate = {
	// Simple Layer (80% of use cases)
	users: paginateUsers,
	organizations: paginateOrganizations,
	organizationMembers: paginateOrganizationMembers,
	invitations: paginateInvitations,

	// Structured Layer (15% of use cases)
	structured: {
		users: paginateUsersStructured,
		organizations: paginateOrganizationsStructured,
		organizationMembers: paginateOrganizationMembersStructured,
	},

	// Cursor Layer (for infinite scroll)
	cursor: {
		users: paginateUsersCursor,
		organizations: paginateOrganizationsCursor,
	},

	// Advanced Layer (5% of use cases) - Direct generic control
	advanced: paginateAdvanced,

	// Export core generic functions for custom use cases
	model: paginateModel,
	modelCursor: paginateModelCursor,
};
